# -*- coding: utf-8 -*-
"""
lie_algebra.py - 李代数模块 (Task 2.5: 动态李代数支持)
用于连接连续市场变化的数学框架

主要特性：
1. 李代数生成器定义
2. 指数映射 exp: g → G
3. 连续市场变化建模
4. 动态基底更新
"""

import numpy as np
import math
from typing import List, Tuple, Dict, Any, Optional
from collections import deque
import warnings
warnings.filterwarnings('ignore')


class LieAlgebraGenerator:
    """李代数生成器"""
    
    def __init__(self, dimension: int = 3):
        self.dimension = dimension
        self.generators = self._initialize_generators()
        self.structure_constants = self._compute_structure_constants()
        
    def _initialize_generators(self) -> List[np.ndarray]:
        """初始化李代数生成器"""
        generators = []
        
        # 市场趋势生成器 (X方向)
        trend_gen = np.array([
            [0, 1, 0],
            [0, 0, 1], 
            [0, 0, 0]
        ])
        generators.append(trend_gen)
        
        # 波动率生成器 (Y方向)
        volatility_gen = np.array([
            [0, 0, 1],
            [0, 0, 0],
            [0, 0, 0]
        ])
        generators.append(volatility_gen)
        
        # 情绪生成器 (Z方向)
        sentiment_gen = np.array([
            [0, 0, 0],
            [1, 0, 0],
            [0, 1, 0]
        ])
        generators.append(sentiment_gen)
        
        return generators
        
    def _compute_structure_constants(self) -> np.ndarray:
        """计算结构常数 [X_i, X_j] = C_ij^k X_k"""
        n = len(self.generators)
        constants = np.zeros((n, n, n))
        
        for i in range(n):
            for j in range(n):
                # 计算李括号 [X_i, X_j] = X_i X_j - X_j X_i
                commutator = (np.dot(self.generators[i], self.generators[j]) - 
                            np.dot(self.generators[j], self.generators[i]))
                
                # 将结果表示为生成器的线性组合
                for k in range(n):
                    # 简化的结构常数计算
                    constants[i, j, k] = np.trace(np.dot(commutator, self.generators[k]))
                    
        return constants
        
    def lie_bracket(self, gen1_idx: int, gen2_idx: int) -> np.ndarray:
        """计算李括号"""
        if gen1_idx >= len(self.generators) or gen2_idx >= len(self.generators):
            raise ValueError("生成器索引超出范围")
            
        return (np.dot(self.generators[gen1_idx], self.generators[gen2_idx]) - 
                np.dot(self.generators[gen2_idx], self.generators[gen1_idx]))


class ExponentialMap:
    """指数映射 exp: g → G"""
    
    def __init__(self, generators: List[np.ndarray]):
        self.generators = generators
        
    def exponential_map(self, coefficients: np.ndarray, t: float = 1.0) -> np.ndarray:
        """计算指数映射 exp(t * Σ c_i X_i)"""
        if len(coefficients) != len(self.generators):
            raise ValueError("系数数量与生成器数量不匹配")
            
        # 构造李代数元素
        lie_element = np.zeros_like(self.generators[0])
        for i, coeff in enumerate(coefficients):
            lie_element += coeff * self.generators[i]
            
        # 计算矩阵指数 (使用泰勒级数近似)
        return self._matrix_exponential(t * lie_element)
        
    def _matrix_exponential(self, matrix: np.ndarray, max_terms: int = 10) -> np.ndarray:
        """计算矩阵指数 exp(A) = I + A + A²/2! + A³/3! + ..."""
        result = np.eye(matrix.shape[0])
        term = np.eye(matrix.shape[0])
        
        for n in range(1, max_terms + 1):
            term = np.dot(term, matrix) / n
            result += term
            
            # 检查收敛性
            if np.linalg.norm(term) < 1e-10:
                break
                
        return result


class MarketLieAlgebra:
    """市场李代数系统"""
    
    def __init__(self):
        self.generator = LieAlgebraGenerator()
        self.exp_map = ExponentialMap(self.generator.generators)
        self.market_history = deque(maxlen=100)
        self.basis_update_counter = 0
        
    def update_market_state(self, trend: float, volatility: float, sentiment: float):
        """更新市场状态"""
        market_state = np.array([trend, volatility, sentiment])
        self.market_history.append(market_state)
        
        # 定期更新李代数基底
        self.basis_update_counter += 1
        if self.basis_update_counter % 20 == 0:
            self._update_basis()
            
    def _update_basis(self):
        """动态更新李代数基底"""
        if len(self.market_history) < 10:
            return
            
        # 分析市场历史数据的主要模式
        history_matrix = np.array(list(self.market_history))
        
        try:
            # 使用主成分分析更新基底方向
            cov_matrix = np.cov(history_matrix.T)
            eigenvalues, eigenvectors = np.linalg.eig(cov_matrix)
            
            # 根据特征向量更新生成器
            for i, eigenvec in enumerate(eigenvectors.T):
                if i < len(self.generator.generators):
                    # 构造新的生成器矩阵
                    new_gen = np.zeros((3, 3))
                    new_gen[0, 1] = eigenvec[0]
                    new_gen[1, 2] = eigenvec[1] 
                    new_gen[0, 2] = eigenvec[2]
                    
                    # 平滑更新（避免剧烈变化）
                    alpha = 0.1
                    self.generator.generators[i] = (
                        (1 - alpha) * self.generator.generators[i] + 
                        alpha * new_gen
                    )
                    
        except Exception as e:
            print(f"李代数基底更新错误: {e}")
            
    def map_to_group(self, market_coefficients: np.ndarray, time_step: float = 1.0) -> np.ndarray:
        """将市场系数映射到群元素"""
        return self.exp_map.exponential_map(market_coefficients, time_step)
        
    def compute_market_flow(self, current_state: np.ndarray, 
                          target_state: np.ndarray) -> np.ndarray:
        """计算市场流动轨迹"""
        # 计算从当前状态到目标状态的李代数元素
        diff = target_state - current_state
        
        # 使用指数映射计算群元素
        group_element = self.map_to_group(diff)
        
        return group_element
        
    def predict_market_evolution(self, steps: int = 5) -> List[np.ndarray]:
        """预测市场演化"""
        if len(self.market_history) < 3:
            return []
            
        # 计算市场变化趋势
        recent_states = list(self.market_history)[-3:]
        velocity = recent_states[-1] - recent_states[-2]
        acceleration = (recent_states[-1] - recent_states[-2]) - (recent_states[-2] - recent_states[-3])
        
        predictions = []
        current_state = recent_states[-1]
        
        for step in range(1, steps + 1):
            # 使用李代数预测下一状态
            predicted_velocity = velocity + acceleration * step * 0.1
            predicted_state = current_state + predicted_velocity * step
            
            # 通过群作用平滑预测
            group_element = self.map_to_group(predicted_velocity, step * 0.1)
            smoothed_prediction = np.dot(group_element, predicted_state.reshape(-1, 1)).flatten()
            
            predictions.append(smoothed_prediction)
            
        return predictions


def create_market_lie_algebra() -> MarketLieAlgebra:
    """创建市场李代数实例"""
    return MarketLieAlgebra()


# 测试代码
if __name__ == "__main__":
    # 创建市场李代数
    market_algebra = create_market_lie_algebra()
    
    # 模拟市场数据
    import random
    for i in range(50):
        trend = random.uniform(-1, 1)
        volatility = random.uniform(0, 1)
        sentiment = random.uniform(-1, 1)
        
        market_algebra.update_market_state(trend, volatility, sentiment)
        
    # 预测市场演化
    predictions = market_algebra.predict_market_evolution(5)
    print(f"市场演化预测: {len(predictions)} 步")
    
    for i, pred in enumerate(predictions):
        print(f"步骤 {i+1}: 趋势={pred[0]:.3f}, 波动率={pred[1]:.3f}, 情绪={pred[2]:.3f}")
