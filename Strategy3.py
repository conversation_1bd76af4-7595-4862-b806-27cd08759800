# -*- coding: utf-8 -*-
"""
Strategy3.py - 高级模糊推理交易策略 (无限易Pro兼容版本)
基于无限易Pro交易软件架构的综合交易策略

主要特性：
1. 完全兼容无限易Pro交易软件架构
2. HULL+STC技术指标主信号
3. 模糊推理 -> 控制论 -> 机器学习 逐级辅助判断
4. 参数可控：通过Params类设置各模块开启状态
5. 状态管理：通过State类管理策略状态
6. K线驱动：基于K线数据进行策略计算

架构说明：
- 继承BaseStrategy基类
- 使用Params和State类管理参数和状态
- 实现on_tick, callback等标准接口
- 支持主图和副图指标显示
"""

from typing import Literal, Callable, List, Tuple, Any, Dict, Optional
import numpy as np
from datetime import datetime, timedelta
import math
import time
import threading
from concurrent.futures import ThreadPoolExecutor
import functools
from queue import Queue
from threading import Thread, Lock
from collections import deque
import warnings
warnings.filterwarnings('ignore')

# 导入无限易Pro基础模块（如果可用）
try:
    from pythongo.base import BaseParams, BaseState, Field
    from pythongo.classdef import KLineData, OrderData, TickData, TradeData
    from pythongo.ui import BaseStrategy
    from pythongo.utils import KLineGenerator
    PYTHONGO_AVAILABLE = True
except ImportError:
    # 模拟无限易Pro模块（用于测试）
    PYTHONGO_AVAILABLE = False

    class Field:
        def __init__(self, default=None, title="", **kwargs):
            self.default = default
            self.title = title

    class BaseParams:
        pass

    class BaseState:
        pass

    class BaseStrategy:
        def __init__(self):
            self.trading = False
            self.widget = None

        def on_tick(self, tick):
            pass

        def on_start(self):
            pass

        def on_stop(self):
            pass

        def on_order_cancel(self, order):
            pass

        def on_trade(self, trade, log=False):
            pass

        def get_position(self, instrument_id):
            class Position:
                def __init__(self):
                    self.net_position = 0
            return Position()

        def send_order(self, **kwargs):
            return "test_order_id"

        def cancel_order(self, order_id):
            pass

        def auto_close_position(self, **kwargs):
            return "test_close_order_id"

        def update_status_bar(self):
            pass

    class KLineData:
        def __init__(self, open=100, high=101, low=99, close=100.5, volume=1000):
            self.open = open
            self.high = high
            self.low = low
            self.close = close
            self.volume = volume

    class TickData:
        def __init__(self, last_price=100, volume=1000):
            self.last_price = last_price
            self.volume = volume
            self.ask_price1 = last_price + 0.01
            self.bid_price1 = last_price - 0.01
            self.ask_price2 = last_price + 0.02
            self.bid_price2 = last_price - 0.02

    class OrderData:
        pass

    class TradeData:
        def __init__(self, direction="buy", price=100, volume=1):
            self.direction = direction
            self.price = price
            self.volume = volume

    class KLineGenerator:
        def __init__(self, **kwargs):
            self.callback = kwargs.get('callback')
            self.real_time_callback = kwargs.get('real_time_callback')

        def tick_to_kline(self, tick):
            pass

        def push_history_data(self):
            pass
# 智能优化库导入（可选）
try:
    from sklearn.ensemble import RandomForestRegressor
    from sklearn.preprocessing import StandardScaler
    AUTOML_AVAILABLE = True
except ImportError:
    AUTOML_AVAILABLE = False

# ==================== 策略参数配置 ====================

class Params(BaseParams):
    """参数映射模型 - 兼容无限易Pro架构"""
    def __init__(self):
        super().__init__()
        self.exchange: str = ""
        self.instrument_id: str = ""
        self.kline_style: str = "M1"

        # 主技术指标参数
        self.hull_period: int = 9
        self.stc_fast_period: int = 23
        self.stc_slow_period: int = 50
        self.stc_cycle_period: int = 10

        # 交易参数
        self.order_volume: int = 1
        self.price_type: str = "D1"
        self.trade_direction: str = "buy"

        # 模块开关
        self.enable_fuzzy: bool = True
        self.enable_control: bool = True
        self.enable_ml: bool = True

        # 风险管理参数
        self.stop_loss_pct: float = 0.02
        self.take_profit_pct: float = 0.04

        # 信号阈值
        self.signal_threshold: float = 0.3

class State(BaseState):
    """状态映射模型 - 兼容无限易Pro架构"""
    def __init__(self):
        super().__init__()
        # 主图指标状态
        self.hull_ma: float = 0.0
        self.stc_value: float = 50.0
        self.stc_signal: float = 50.0

        # 副图指标状态
        self.fuzzy_signal: float = 0.0
        self.control_signal: float = 0.0
        self.ml_signal: float = 0.0
        self.final_signal: float = 0.0

        # 系统状态
        self.volatility: float = 0.0
        self.trend_strength: float = 0.0
        self.system_stability: float = 1.0

        # 交易状态
        self.position_size: float = 0.0
        self.entry_price: float = 0.0
        self.unrealized_pnl: float = 0.0

        # 性能指标
        self.total_trades: int = 0
        self.winning_trades: int = 0
        self.total_profit: float = 0.0
        self.win_rate: float = 0.0

# ==================== 事件系统 ====================

class TradingEvent:
    """交易事件基类"""
    def __init__(self, event_type: str, timestamp: float, data: Dict[str, Any]):
        self.event_type = event_type
        self.timestamp = timestamp
        self.data = data
        self.processed = False
        self.result = None

class SignalEvent(TradingEvent):
    """信号事件"""
    def __init__(self, timestamp: float, signal_data: Dict[str, Any]):
        super().__init__("signal", timestamp, signal_data)

class CorrectionEvent(TradingEvent):
    """校正事件"""
    def __init__(self, timestamp: float, correction_data: Dict[str, Any]):
        super().__init__("correction", timestamp, correction_data)

class DecisionEvent(TradingEvent):
    """决策事件"""
    def __init__(self, timestamp: float, decision_data: Dict[str, Any]):
        super().__init__("decision", timestamp, decision_data)

class EventBus:
    """事件总线"""
    def __init__(self):
        self.subscribers = {}
        self.event_queue = Queue()
        self.processing = False

    def subscribe(self, event_type: str, handler: Callable):
        """订阅事件"""
        if event_type not in self.subscribers:
            self.subscribers[event_type] = []
        self.subscribers[event_type].append(handler)

    def publish(self, event: TradingEvent):
        """发布事件"""
        self.event_queue.put(event)

    def process_events(self):
        """处理事件队列"""
        while not self.event_queue.empty():
            try:
                event = self.event_queue.get_nowait()
                if event.event_type in self.subscribers:
                    for handler in self.subscribers[event.event_type]:
                        try:
                            result = handler(event)
                            event.result = result
                            event.processed = True
                        except Exception as e:
                            print(f"事件处理错误: {e}")
            except:
                break

# ==================== 技术指标实现 ====================

class HullMovingAverage:
    """Hull移动平均线"""
    def __init__(self, period: int = 9):
        self.period = period
        self.values = deque(maxlen=period * 2)
        self.wma_half = deque(maxlen=period // 2)
        self.wma_full = deque(maxlen=period)

    def update(self, price: float) -> Optional[float]:
        """更新Hull MA值"""
        self.values.append(price)

        if len(self.values) < self.period:
            return None

        # 计算WMA(period/2) * 2
        half_period = self.period // 2
        wma_half = self._calculate_wma(list(self.values)[-half_period:], half_period)

        # 计算WMA(period)
        wma_full = self._calculate_wma(list(self.values), self.period)

        # Hull MA = WMA(2*WMA(period/2) - WMA(period), sqrt(period))
        hull_raw = 2 * wma_half - wma_full
        self.wma_half.append(hull_raw)

        sqrt_period = int(math.sqrt(self.period))
        if len(self.wma_half) >= sqrt_period:
            hull_ma = self._calculate_wma(list(self.wma_half)[-sqrt_period:], sqrt_period)
            return hull_ma

        return None

    def _calculate_wma(self, values: List[float], period: int) -> float:
        """计算加权移动平均"""
        if len(values) < period:
            return sum(values) / len(values)

        weights = list(range(1, period + 1))
        weighted_sum = sum(v * w for v, w in zip(values[-period:], weights))
        weight_sum = sum(weights)
        return weighted_sum / weight_sum

class SchaffTrendCycle:
    """Schaff趋势周期指标"""
    def __init__(self, fast_period: int = 23, slow_period: int = 50, cycle_period: int = 10):
        self.fast_period = fast_period
        self.slow_period = slow_period
        self.cycle_period = cycle_period
        self.prices = deque(maxlen=max(fast_period, slow_period) + cycle_period)
        self.macd_values = deque(maxlen=cycle_period * 2)
        self.stoch_values = deque(maxlen=cycle_period * 2)

    def update(self, high: float, low: float, close: float) -> Tuple[Optional[float], Optional[float]]:
        """更新STC值"""
        self.prices.append({'high': high, 'low': low, 'close': close})

        if len(self.prices) < self.slow_period:
            return None, None

        # 计算MACD
        macd = self._calculate_macd()
        if macd is None:
            return None, None

        self.macd_values.append(macd)

        # 计算第一次随机指标
        if len(self.macd_values) >= self.cycle_period:
            stoch1 = self._calculate_stochastic(list(self.macd_values), self.cycle_period)
            self.stoch_values.append(stoch1)

            # 计算第二次随机指标（STC值）
            if len(self.stoch_values) >= self.cycle_period:
                stc_value = self._calculate_stochastic(list(self.stoch_values), self.cycle_period)

                # 计算信号线（STC的移动平均）
                signal_period = 3
                if len(self.stoch_values) >= signal_period:
                    signal_line = sum(list(self.stoch_values)[-signal_period:]) / signal_period
                    return stc_value, signal_line

        return None, None

    def _calculate_macd(self) -> Optional[float]:
        """计算MACD"""
        closes = [p['close'] for p in self.prices]

        if len(closes) < self.slow_period:
            return None

        # 计算EMA
        fast_ema = self._calculate_ema(closes, self.fast_period)
        slow_ema = self._calculate_ema(closes, self.slow_period)

        return fast_ema - slow_ema

    def _calculate_ema(self, values: List[float], period: int) -> float:
        """计算指数移动平均"""
        if len(values) < period:
            return sum(values) / len(values)

        multiplier = 2.0 / (period + 1)
        ema = values[0]

        for value in values[1:]:
            ema = (value * multiplier) + (ema * (1 - multiplier))

        return ema

    def _calculate_stochastic(self, values: List[float], period: int) -> float:
        """计算随机指标"""
        if len(values) < period:
            return 50.0

        recent_values = values[-period:]
        highest = max(recent_values)
        lowest = min(recent_values)
        current = values[-1]

        if highest == lowest:
            return 50.0

        stoch = ((current - lowest) / (highest - lowest)) * 100
        return max(0, min(100, stoch))

# ==================== 模糊推理系统 ====================

class IntervalValuedFuzzySet:
    """区间值模糊集"""
    def __init__(self, lower_membership: Callable[[float], float], upper_membership: Callable[[float], float]):
        self.lower_membership = lower_membership
        self.upper_membership = upper_membership

    def membership_interval(self, x: float) -> Tuple[float, float]:
        """计算隶属度区间"""
        lower = max(0, min(1, self.lower_membership(x)))
        upper = max(0, min(1, self.upper_membership(x)))
        return (lower, min(upper, 1.0))

class IntuitiveFuzzySet:
    """直觉模糊集"""
    def __init__(self, membership_func: Callable[[float], float], non_membership_func: Callable[[float], float]):
        self.membership_func = membership_func
        self.non_membership_func = non_membership_func

    def membership_degree(self, x: float) -> Tuple[float, float, float]:
        """计算隶属度、非隶属度和犹豫度"""
        mu = max(0, min(1, self.membership_func(x)))
        nu = max(0, min(1, self.non_membership_func(x)))

        # 确保 mu + nu <= 1
        if mu + nu > 1:
            total = mu + nu
            mu = mu / total
            nu = nu / total

        pi = 1 - mu - nu  # 犹豫度
        return (mu, nu, pi)

class PythagoreanFuzzySet:
    """Pythagorean模糊集"""
    def __init__(self, membership_func: Callable[[float], float], non_membership_func: Callable[[float], float]):
        self.membership_func = membership_func
        self.non_membership_func = non_membership_func

    def membership_degree(self, x: float) -> Tuple[float, float, float]:
        """计算隶属度、非隶属度和犹豫度"""
        mu = max(0, min(1, self.membership_func(x)))
        nu = max(0, min(1, self.non_membership_func(x)))

        # 确保 mu² + nu² <= 1
        if mu**2 + nu**2 > 1:
            norm = math.sqrt(mu**2 + nu**2)
            mu = mu / norm
            nu = nu / norm

        pi = math.sqrt(1 - mu**2 - nu**2)  # 犹豫度
        return (mu, nu, pi)

class TraditionalFuzzySet:
    """传统模糊集"""
    def __init__(self, membership_func: Callable[[float], float]):
        self.membership_func = membership_func

    def membership_degree(self, x: float) -> float:
        """计算隶属度"""
        return max(0, min(1, self.membership_func(x)))

class FuzzyRuleEngine:
    """模糊规则引擎"""
    def __init__(self):
        self.rules = []
        self.variables = {}

    def add_variable(self, name: str, fuzzy_sets: Dict[str, Any]):
        """添加模糊变量"""
        self.variables[name] = fuzzy_sets

    def add_rule(self, conditions: Dict[str, str], conclusion: str, weight: float = 1.0):
        """添加模糊规则"""
        self.rules.append({
            'conditions': conditions,
            'conclusion': conclusion,
            'weight': weight
        })

    def evaluate(self, inputs: Dict[str, float]) -> Dict[str, float]:
        """评估模糊规则"""
        results = {}

        for rule in self.rules:
            # 计算规则激活度
            activation = self._calculate_activation(rule['conditions'], inputs)

            if activation > 0:
                conclusion = rule['conclusion']
                if conclusion not in results:
                    results[conclusion] = 0
                results[conclusion] = max(results[conclusion], activation * rule['weight'])

        return results

    def _calculate_activation(self, conditions: Dict[str, str], inputs: Dict[str, float]) -> float:
        """计算规则激活度"""
        activations = []

        for var_name, fuzzy_set_name in conditions.items():
            if var_name in inputs and var_name in self.variables:
                fuzzy_set = self.variables[var_name][fuzzy_set_name]
                value = inputs[var_name]

                if isinstance(fuzzy_set, TraditionalFuzzySet):
                    activation = fuzzy_set.membership_degree(value)
                elif isinstance(fuzzy_set, IntervalValuedFuzzySet):
                    lower, upper = fuzzy_set.membership_interval(value)
                    activation = (lower + upper) / 2
                elif isinstance(fuzzy_set, (IntuitiveFuzzySet, PythagoreanFuzzySet)):
                    mu, nu, pi = fuzzy_set.membership_degree(value)
                    activation = mu
                else:
                    activation = 0

                activations.append(activation)

        # 使用最小值作为AND操作
        return min(activations) if activations else 0

class AdvancedFuzzySystem:
    """高级模糊推理系统"""
    def __init__(self):
        self.rule_engine = FuzzyRuleEngine()
        self._initialize_fuzzy_sets()
        self._initialize_rules()

    def _initialize_fuzzy_sets(self):
        """初始化模糊集"""
        # 价格趋势模糊集
        trend_sets = {
            'strong_down': TraditionalFuzzySet(lambda x: max(0, (-x + 0.5) / 0.5) if x <= 0 else 0),
            'down': TraditionalFuzzySet(lambda x: max(0, 1 - abs(x + 0.25) / 0.25) if -0.5 <= x <= 0 else 0),
            'neutral': TraditionalFuzzySet(lambda x: max(0, 1 - abs(x) / 0.25) if -0.25 <= x <= 0.25 else 0),
            'up': TraditionalFuzzySet(lambda x: max(0, 1 - abs(x - 0.25) / 0.25) if 0 <= x <= 0.5 else 0),
            'strong_up': TraditionalFuzzySet(lambda x: max(0, (x - 0.5) / 0.5) if x >= 0 else 0)
        }

        # 波动率模糊集
        volatility_sets = {
            'low': TraditionalFuzzySet(lambda x: max(0, (0.02 - x) / 0.02) if x <= 0.02 else 0),
            'medium': TraditionalFuzzySet(lambda x: max(0, 1 - abs(x - 0.03) / 0.01) if 0.02 <= x <= 0.04 else 0),
            'high': TraditionalFuzzySet(lambda x: max(0, (x - 0.04) / 0.02) if x >= 0.04 else 0)
        }

        # 成交量模糊集
        volume_sets = {
            'low': TraditionalFuzzySet(lambda x: max(0, (0.5 - x) / 0.5) if x <= 0.5 else 0),
            'normal': TraditionalFuzzySet(lambda x: max(0, 1 - abs(x - 1.0) / 0.5) if 0.5 <= x <= 1.5 else 0),
            'high': TraditionalFuzzySet(lambda x: max(0, (x - 1.5) / 0.5) if x >= 1.5 else 0)
        }

        self.rule_engine.add_variable('trend', trend_sets)
        self.rule_engine.add_variable('volatility', volatility_sets)
        self.rule_engine.add_variable('volume', volume_sets)

    def _initialize_rules(self):
        """初始化模糊规则"""
        # 买入规则
        self.rule_engine.add_rule({'trend': 'strong_up', 'volatility': 'low'}, 'strong_buy', 0.9)
        self.rule_engine.add_rule({'trend': 'up', 'volatility': 'low', 'volume': 'high'}, 'buy', 0.8)
        self.rule_engine.add_rule({'trend': 'up', 'volatility': 'medium'}, 'weak_buy', 0.6)

        # 卖出规则
        self.rule_engine.add_rule({'trend': 'strong_down', 'volatility': 'low'}, 'strong_sell', 0.9)
        self.rule_engine.add_rule({'trend': 'down', 'volatility': 'low', 'volume': 'high'}, 'sell', 0.8)
        self.rule_engine.add_rule({'trend': 'down', 'volatility': 'medium'}, 'weak_sell', 0.6)

        # 持有规则
        self.rule_engine.add_rule({'trend': 'neutral', 'volatility': 'low'}, 'hold', 0.7)
        self.rule_engine.add_rule({'volatility': 'high'}, 'hold', 0.5)

    def process_signal(self, market_data: Dict[str, float]) -> Dict[str, float]:
        """处理市场信号"""
        return self.rule_engine.evaluate(market_data)

# ==================== 控制论系统 ====================

class PIDController:
    """PID控制器"""
    def __init__(self, kp: float = 0.5, ki: float = 0.1, kd: float = 0.2):
        self.kp = kp
        self.ki = ki
        self.kd = kd
        self.previous_error = 0.0
        self.integral = 0.0
        self.derivative = 0.0

    def update(self, setpoint: float, measured_value: float, dt: float = 1.0) -> float:
        """更新PID控制器"""
        error = setpoint - measured_value

        # 积分项
        self.integral += error * dt

        # 微分项
        self.derivative = (error - self.previous_error) / dt if dt > 0 else 0

        # PID输出
        output = (self.kp * error +
                 self.ki * self.integral +
                 self.kd * self.derivative)

        self.previous_error = error
        return output

    def reset(self):
        """重置PID控制器"""
        self.previous_error = 0.0
        self.integral = 0.0
        self.derivative = 0.0

class KalmanFilter:
    """卡尔曼滤波器"""
    def __init__(self, process_noise: float = 0.01, measurement_noise: float = 0.1):
        self.process_noise = process_noise
        self.measurement_noise = measurement_noise
        self.estimate = 0.0
        self.error_covariance = 1.0

    def update(self, measurement: float) -> float:
        """更新卡尔曼滤波器"""
        # 预测步骤
        predicted_estimate = self.estimate
        predicted_error_covariance = self.error_covariance + self.process_noise

        # 更新步骤
        kalman_gain = predicted_error_covariance / (predicted_error_covariance + self.measurement_noise)
        self.estimate = predicted_estimate + kalman_gain * (measurement - predicted_estimate)
        self.error_covariance = (1 - kalman_gain) * predicted_error_covariance

        return self.estimate

class LyapunovAnalyzer:
    """李雅普诺夫稳定性分析器"""
    def __init__(self, window_size: int = 50):
        self.window_size = window_size
        self.price_history = deque(maxlen=window_size)
        self.returns_history = deque(maxlen=window_size)

    def update(self, price: float):
        """更新价格数据"""
        if len(self.price_history) > 0:
            return_rate = (price - self.price_history[-1]) / self.price_history[-1]
            self.returns_history.append(return_rate)

        self.price_history.append(price)

    def calculate_stability(self) -> float:
        """计算系统稳定性"""
        if len(self.returns_history) < 10:
            return 0.5

        returns = np.array(list(self.returns_history))

        # 计算李雅普诺夫指数
        try:
            # 简化的李雅普诺夫指数计算
            mean_return = np.mean(returns)
            std_return = np.std(returns)

            if std_return == 0:
                return 1.0

            # 稳定性指标：负的李雅普诺夫指数表示稳定
            lyapunov_exponent = mean_return / std_return
            stability = 1.0 / (1.0 + abs(lyapunov_exponent))

            return float(max(0, min(1, stability)))
        except:
            return 0.5

class ControlTheoryProcessor:
    """控制论处理器"""
    def __init__(self, params: Params):
        # 使用默认参数，因为Params类中没有这些参数
        self.pid_controller = PIDController(0.5, 0.1, 0.2)
        self.kalman_filter = KalmanFilter(0.01, 0.1)
        self.lyapunov_analyzer = LyapunovAnalyzer()
        self.target_return = 0.0

    def process_signal(self, signal_strength: float, current_price: float,
                      target_price: float) -> Dict[str, float]:
        """处理控制论信号"""
        # 更新李雅普诺夫分析器
        self.lyapunov_analyzer.update(current_price)

        # 卡尔曼滤波平滑价格
        filtered_price = self.kalman_filter.update(current_price)

        # PID控制调整
        pid_output = self.pid_controller.update(target_price, filtered_price)

        # 计算系统稳定性
        stability = self.lyapunov_analyzer.calculate_stability()

        # 控制论校正信号
        control_adjustment = pid_output * stability
        corrected_signal = signal_strength + control_adjustment * 0.1

        return {
            'corrected_signal': max(-1, min(1, corrected_signal)),
            'stability': stability,
            'pid_output': pid_output,
            'filtered_price': filtered_price
        }

# ==================== 机器学习系统 ====================

class MLFeatureExtractor:
    """机器学习特征提取器"""
    def __init__(self, window_size: int = 20):
        self.window_size = window_size
        self.price_history = deque(maxlen=window_size)
        self.volume_history = deque(maxlen=window_size)

    def update(self, price: float, volume: float):
        """更新历史数据"""
        self.price_history.append(price)
        self.volume_history.append(volume)

    def extract_features(self) -> Optional[np.ndarray]:
        """提取特征"""
        if len(self.price_history) < self.window_size:
            return None

        prices = np.array(list(self.price_history))
        volumes = np.array(list(self.volume_history))

        features = []

        # 价格特征
        features.extend([
            np.mean(prices),  # 平均价格
            np.std(prices),   # 价格标准差
            (prices[-1] - prices[0]) / prices[0],  # 总收益率
            np.max(prices) / np.min(prices) - 1,   # 价格波动范围
        ])

        # 技术指标特征
        if len(prices) >= 5:
            sma_5 = np.mean(prices[-5:])
            features.append((prices[-1] - sma_5) / sma_5)  # 相对SMA5位置
        else:
            features.append(0)

        if len(prices) >= 10:
            sma_10 = np.mean(prices[-10:])
            features.append((prices[-1] - sma_10) / sma_10)  # 相对SMA10位置
        else:
            features.append(0)

        # 成交量特征
        features.extend([
            np.mean(volumes),  # 平均成交量
            np.std(volumes),   # 成交量标准差
            volumes[-1] / np.mean(volumes) if np.mean(volumes) > 0 else 1,  # 相对成交量
        ])

        # 动量特征
        if len(prices) >= 3:
            momentum = (prices[-1] - prices[-3]) / prices[-3]
            features.append(momentum)
        else:
            features.append(0)

        # RSI特征
        if len(prices) >= 14:
            rsi = self._calculate_rsi(prices, 14)
            features.append(rsi / 100.0)  # 归一化到[0,1]
        else:
            features.append(0.5)

        return np.array(features)

    def _calculate_rsi(self, prices: np.ndarray, period: int = 14) -> float:
        """计算RSI"""
        if len(prices) < period + 1:
            return 50.0

        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)

        avg_gain = np.mean(gains[-period:])
        avg_loss = np.mean(losses[-period:])

        if avg_loss == 0:
            return 100.0

        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        return float(rsi)

class MLPredictor:
    """机器学习预测器"""
    def __init__(self, feature_window: int = 20, update_frequency: int = 10):
        self.feature_extractor = MLFeatureExtractor(feature_window)
        self.update_frequency = update_frequency
        self.update_counter = 0
        self.model = None
        self.scaler = None
        self.training_features = []
        self.training_targets = []
        self.last_prediction = 0.0
        self.prediction_confidence = 0.0

        if AUTOML_AVAILABLE:
            self.model = RandomForestRegressor(n_estimators=50, random_state=42)
            self.scaler = StandardScaler()

    def update(self, price: float, volume: float, future_return: Optional[float] = None):
        """更新数据并训练模型"""
        self.feature_extractor.update(price, volume)

        # 提取特征
        features = self.feature_extractor.extract_features()
        if features is None:
            return

        # 添加训练数据
        if future_return is not None:
            self.training_features.append(features)
            self.training_targets.append(future_return)

            # 限制训练数据大小
            if len(self.training_features) > 1000:
                self.training_features = self.training_features[-800:]
                self.training_targets = self.training_targets[-800:]

        # 定期重新训练模型
        self.update_counter += 1
        if (self.update_counter % self.update_frequency == 0 and
            len(self.training_features) >= 50 and
            AUTOML_AVAILABLE):
            self._retrain_model()

    def predict(self) -> Tuple[float, float]:
        """预测未来收益"""
        features = self.feature_extractor.extract_features()
        if features is None or self.model is None or not AUTOML_AVAILABLE:
            return 0.0, 0.0

        try:
            # 标准化特征
            features_scaled = self.scaler.transform(features.reshape(1, -1))

            # 预测
            prediction = self.model.predict(features_scaled)[0]

            # 计算置信度（基于模型的特征重要性和历史准确性）
            confidence = min(0.9, len(self.training_features) / 100.0)

            self.last_prediction = prediction
            self.prediction_confidence = confidence

            return prediction, confidence
        except Exception as e:
            print(f"ML预测错误: {e}")
            return 0.0, 0.0

    def _retrain_model(self):
        """重新训练模型"""
        if not AUTOML_AVAILABLE or len(self.training_features) < 50:
            return

        try:
            X = np.array(self.training_features)
            y = np.array(self.training_targets)

            # 标准化特征
            self.scaler.fit(X)
            X_scaled = self.scaler.transform(X)

            # 训练模型
            self.model.fit(X_scaled, y)

            print(f"ML模型重新训练完成，训练样本数: {len(X)}")
        except Exception as e:
            print(f"ML模型训练错误: {e}")

class MLProcessor:
    """机器学习处理器"""
    def __init__(self, params: Params):
        # 使用默认参数
        self.predictor = MLPredictor(20, 10)
        self.threshold = 0.6

    def process_signal(self, signal_strength: float, price: float, volume: float) -> Dict[str, float]:
        """处理机器学习信号"""
        # 更新预测器
        self.predictor.update(price, volume)

        # 获取预测
        prediction, confidence = self.predictor.predict()

        # 只有当置信度足够高时才调整信号
        if confidence >= self.threshold:
            # 根据预测调整信号强度
            ml_adjustment = prediction * confidence
            adjusted_signal = signal_strength + ml_adjustment * 0.2
        else:
            adjusted_signal = signal_strength
            ml_adjustment = 0.0

        return {
            'adjusted_signal': max(-1, min(1, adjusted_signal)),
            'ml_prediction': prediction,
            'ml_confidence': confidence,
            'ml_adjustment': ml_adjustment
        }

# ==================== 责任链处理器 ====================

class ChainProcessor:
    """责任链处理器基类"""
    def __init__(self):
        self.next_processor = None

    def set_next(self, processor: 'ChainProcessor'):
        """设置下一个处理器"""
        self.next_processor = processor
        return processor

    def process(self, signal_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理信号数据"""
        result = self._handle(signal_data)

        if self.next_processor:
            return self.next_processor.process(result)

        return result

    def _handle(self, signal_data: Dict[str, Any]) -> Dict[str, Any]:
        """具体处理逻辑，由子类实现"""
        return signal_data

class FuzzyProcessor(ChainProcessor):
    """模糊推理处理器"""
    def __init__(self, params: Params):
        super().__init__()
        self.enabled = params.enable_fuzzy
        self.fuzzy_system = AdvancedFuzzySystem()

    def _handle(self, signal_data: Dict[str, Any]) -> Dict[str, Any]:
        """模糊推理处理"""
        if not self.enabled:
            signal_data['fuzzy_correction'] = signal_data.get('primary_signal', 0.0)
            signal_data['fuzzy_confidence'] = 0.0
            return signal_data

        # 准备模糊推理输入
        market_data = {
            'trend': signal_data.get('trend_strength', 0.0),
            'volatility': signal_data.get('volatility', 0.03),
            'volume': signal_data.get('volume_ratio', 1.0)
        }

        # 执行模糊推理
        fuzzy_results = self.fuzzy_system.process_signal(market_data)

        # 计算模糊校正信号
        primary_signal = signal_data.get('primary_signal', 0.0)

        # 根据模糊推理结果调整信号
        if 'strong_buy' in fuzzy_results:
            correction = fuzzy_results['strong_buy'] * 0.8
        elif 'buy' in fuzzy_results:
            correction = fuzzy_results['buy'] * 0.6
        elif 'weak_buy' in fuzzy_results:
            correction = fuzzy_results['weak_buy'] * 0.3
        elif 'strong_sell' in fuzzy_results:
            correction = -fuzzy_results['strong_sell'] * 0.8
        elif 'sell' in fuzzy_results:
            correction = -fuzzy_results['sell'] * 0.6
        elif 'weak_sell' in fuzzy_results:
            correction = -fuzzy_results['weak_sell'] * 0.3
        else:
            correction = 0.0

        # 融合主信号和模糊校正
        corrected_signal = primary_signal * 0.7 + correction * 0.3

        signal_data['fuzzy_correction'] = max(-1, min(1, corrected_signal))
        signal_data['fuzzy_confidence'] = max(fuzzy_results.values()) if fuzzy_results else 0.0
        signal_data['fuzzy_results'] = fuzzy_results

        return signal_data

class ControlProcessor(ChainProcessor):
    """控制论处理器"""
    def __init__(self, params: Params):
        super().__init__()
        self.enabled = params.enable_control
        self.control_processor = ControlTheoryProcessor(params)

    def _handle(self, signal_data: Dict[str, Any]) -> Dict[str, Any]:
        """控制论处理"""
        if not self.enabled:
            signal_data['control_correction'] = signal_data.get('fuzzy_correction',
                                                              signal_data.get('primary_signal', 0.0))
            return signal_data

        # 获取当前信号强度
        current_signal = signal_data.get('fuzzy_correction',
                                       signal_data.get('primary_signal', 0.0))

        # 控制论处理
        current_price = signal_data.get('current_price', 0.0)
        target_price = signal_data.get('target_price', current_price)

        control_results = self.control_processor.process_signal(
            current_signal, current_price, target_price
        )

        signal_data['control_correction'] = control_results['corrected_signal']
        signal_data['system_stability'] = control_results['stability']
        signal_data['pid_output'] = control_results['pid_output']
        signal_data['filtered_price'] = control_results['filtered_price']

        return signal_data

class MLProcessor_Chain(ChainProcessor):
    """机器学习处理器（责任链版本）"""
    def __init__(self, params: Params):
        super().__init__()
        self.enabled = params.enable_ml
        self.ml_processor = MLProcessor(params)

    def _handle(self, signal_data: Dict[str, Any]) -> Dict[str, Any]:
        """机器学习处理"""
        if not self.enabled:
            signal_data['final_signal'] = signal_data.get('control_correction',
                                                         signal_data.get('fuzzy_correction',
                                                                       signal_data.get('primary_signal', 0.0)))
            return signal_data

        # 获取当前信号强度
        current_signal = signal_data.get('control_correction',
                                       signal_data.get('fuzzy_correction',
                                                     signal_data.get('primary_signal', 0.0)))

        # 机器学习处理
        price = signal_data.get('current_price', 0.0)
        volume = signal_data.get('volume', 0.0)

        ml_results = self.ml_processor.process_signal(current_signal, price, volume)

        signal_data['final_signal'] = ml_results['adjusted_signal']
        signal_data['ml_prediction'] = ml_results['ml_prediction']
        signal_data['ml_confidence'] = ml_results['ml_confidence']
        signal_data['ml_adjustment'] = ml_results['ml_adjustment']

        return signal_data

class SignalProcessingChain:
    """信号处理责任链"""
    def __init__(self, params: Params):
        self.params = params

        # 构建责任链
        self.fuzzy_processor = FuzzyProcessor(params)
        self.control_processor = ControlProcessor(params)
        self.ml_processor = MLProcessor_Chain(params)

        # 链接处理器
        self.fuzzy_processor.set_next(self.control_processor).set_next(self.ml_processor)

    def process_signal(self, signal_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理信号通过责任链"""
        return self.fuzzy_processor.process(signal_data)

# ==================== 主策略类 ====================

class Strategy3(BaseStrategy):
    """高级模糊推理交易策略 - 兼容无限易Pro架构"""

    def __init__(self):
        super().__init__()
        self.params_map = Params()
        """参数表"""

        self.state_map = State()
        """状态表"""

        # 交易信号
        self.buy_signal: bool = False
        self.sell_signal: bool = False
        self.cover_signal: bool = False
        self.short_signal: bool = False

        # 当前tick数据
        self.tick: TickData = None

        # 技术指标历史值
        self.hull_history = deque(maxlen=100)
        self.stc_history = deque(maxlen=100)
        self.stc_signal_history = deque(maxlen=100)

        # 模糊推理系统
        self.fuzzy_system = AdvancedFuzzySystem() if self.params_map.enable_fuzzy else None

        # 控制论系统
        self.control_system = None
        if self.params_map.enable_control:
            self.control_system = ControlTheoryProcessor(self.params_map)

        # 机器学习系统
        self.ml_system = None
        if self.params_map.enable_ml and AUTOML_AVAILABLE:
            self.ml_system = MLProcessor(self.params_map)

        # 订单管理
        self.order_id = None
        self.signal_price = 0

        print("Strategy3 高级模糊推理策略初始化完成")

    @property
    def main_indicator_data(self) -> dict[str, float]:
        """主图指标"""
        return {
            f"HULL{self.params_map.hull_period}": float(self.state_map.hull_ma),
            f"STC": float(self.state_map.stc_value),
            f"STC_SIGNAL": float(self.state_map.stc_signal),
            "TREND": float(self.state_map.trend_strength)
        }

    @property
    def sub_indicator_data(self) -> dict[str, float]:
        """副图指标"""
        return {
            "FUZZY": float(self.state_map.fuzzy_signal),
            "CONTROL": float(self.state_map.control_signal),
            "ML": float(self.state_map.ml_signal),
            "FINAL": float(self.state_map.final_signal),
            "VOLATILITY": float(self.state_map.volatility) * 100,
            "STABILITY": float(self.state_map.system_stability) * 100
        }

    def on_tick(self, tick: TickData):
        """处理tick数据 - 无限易Pro标准接口"""
        super().on_tick(tick)

        self.tick = tick

        # 将tick数据转换为K线
        self.kline_generator.tick_to_kline(tick)

    def on_order_cancel(self, order: OrderData) -> None:
        """撤单推送回调"""
        super().on_order_cancel(order)
        self.order_id = None

    def on_trade(self, trade: TradeData, log: bool = False) -> None:
        """成交推送回调"""
        super().on_trade(trade, log)
        self.order_id = None

        # 更新交易统计
        self.state_map.total_trades += 1
        if trade.direction == "buy":
            if self.state_map.position_size < 0:  # 平空
                profit = (self.state_map.entry_price - trade.price) * abs(self.state_map.position_size)
            else:  # 开多
                self.state_map.position_size = trade.volume
                self.state_map.entry_price = trade.price
                return
        else:  # sell
            if self.state_map.position_size > 0:  # 平多
                profit = (trade.price - self.state_map.entry_price) * self.state_map.position_size
            else:  # 开空
                self.state_map.position_size = -trade.volume
                self.state_map.entry_price = trade.price
                return

        # 更新盈利统计
        if 'profit' in locals():
            self.state_map.total_profit += profit
            if profit > 0:
                self.state_map.winning_trades += 1
            self.state_map.win_rate = self.state_map.winning_trades / self.state_map.total_trades

    def on_start(self):
        """策略启动"""
        self.kline_generator = KLineGenerator(
            callback=self.callback,
            real_time_callback=self.real_time_callback,
            exchange=self.params_map.exchange,
            instrument_id=self.params_map.instrument_id,
            style=self.params_map.kline_style
        )
        self.kline_generator.push_history_data()

        super().on_start()

        # 重置信号状态
        self.signal_price = 0
        self.buy_signal = False
        self.sell_signal = False
        self.cover_signal = False
        self.short_signal = False
        self.tick = None

        self.update_status_bar()

    def on_stop(self):
        """策略停止"""
        super().on_stop()

    def callback(self, kline: KLineData) -> None:
        """接受K线回调 - 无限易Pro标准接口"""
        try:
            # 计算技术指标
            self.calc_indicator(kline)

            # 计算交易信号
            self.calc_signal(kline)

            # 执行信号
            self.exec_signal()

            # 更新图表
            self.widget.recv_kline({
                "kline": kline,
                "signal_price": self.signal_price,
                **self.main_indicator_data,
                **self.sub_indicator_data
            })

            if self.trading:
                self.update_status_bar()

        except Exception as e:
            print(f"K线回调处理错误: {e}")

    def real_time_callback(self, kline: KLineData) -> None:
        """实时K线回调"""
        try:
            # 计算指标
            self.calc_indicator(kline)

            # 更新图表
            self.widget.recv_kline({
                "kline": kline,
                **self.main_indicator_data,
                **self.sub_indicator_data
            })

            self.update_status_bar()

        except Exception as e:
            print(f"实时K线回调处理错误: {e}")

    def calc_indicator(self, kline: KLineData) -> None:
        """计算技术指标"""
        try:
            # 计算Hull MA
            hull_value = self._calculate_hull_ma(kline.close)
            if hull_value is not None:
                self.state_map.hull_ma = hull_value
                self.hull_history.append(hull_value)

            # 计算STC
            stc_value, stc_signal = self._calculate_stc(kline.high, kline.low, kline.close)
            if stc_value is not None:
                self.state_map.stc_value = stc_value
                self.stc_history.append(stc_value)
            if stc_signal is not None:
                self.state_map.stc_signal = stc_signal
                self.stc_signal_history.append(stc_signal)

            # 计算波动率
            self._calculate_volatility(kline.close)

            # 计算趋势强度
            self._calculate_trend_strength()

        except Exception as e:
            print(f"指标计算错误: {e}")

    def calc_signal(self, kline: KLineData):
        """计算交易信号"""
        try:
            # 重置信号
            self.buy_signal = False
            self.sell_signal = False
            self.cover_signal = False
            self.short_signal = False

            # 生成主信号
            primary_signal = self._generate_primary_signal(kline)

            # 模糊推理校正
            if self.params_map.enable_fuzzy and self.fuzzy_system:
                fuzzy_signal = self._apply_fuzzy_correction(primary_signal, kline)
                self.state_map.fuzzy_signal = fuzzy_signal
            else:
                fuzzy_signal = primary_signal
                self.state_map.fuzzy_signal = 0

            # 控制论校正
            if self.params_map.enable_control and self.control_system:
                control_signal = self._apply_control_correction(fuzzy_signal, kline)
                self.state_map.control_signal = control_signal
            else:
                control_signal = fuzzy_signal
                self.state_map.control_signal = 0

            # 机器学习预测
            if self.params_map.enable_ml and self.ml_system:
                ml_signal = self._apply_ml_prediction(control_signal, kline)
                self.state_map.ml_signal = ml_signal
            else:
                ml_signal = control_signal
                self.state_map.ml_signal = 0

            # 最终信号
            final_signal = ml_signal
            self.state_map.final_signal = final_signal

            # 生成交易信号
            if abs(final_signal) >= self.params_map.signal_threshold:
                if final_signal > 0:
                    self.buy_signal = True
                    self.cover_signal = True
                else:
                    self.sell_signal = True
                    self.short_signal = True

            # 设置交易价格
            self._set_trading_prices(kline)

        except Exception as e:
            print(f"信号计算错误: {e}")

    def _generate_primary_signal(self, kline: KLineData) -> float:
        """生成主信号（HULL+STC）"""
        signal_strength = 0.0

        try:
            # Hull MA信号
            if len(self.hull_history) >= 2:
                hull_trend = self.hull_history[-1] - self.hull_history[-2]
                if hull_trend > 0:
                    hull_signal = 1.0
                elif hull_trend < 0:
                    hull_signal = -1.0
                else:
                    hull_signal = 0.0

                # Hull MA与价格关系
                if self.state_map.hull_ma > 0:
                    price_position = (kline.close - self.state_map.hull_ma) / self.state_map.hull_ma
                    hull_strength = min(1.0, abs(price_position) * 10)
                    signal_strength += hull_signal * hull_strength * 0.6

            # STC信号
            if len(self.stc_history) >= 2 and len(self.stc_signal_history) >= 2:
                prev_stc = self.stc_history[-2]
                prev_signal = self.stc_signal_history[-2]
                curr_stc = self.stc_history[-1]
                curr_signal = self.stc_signal_history[-1]

                # 金叉/死叉检测
                if prev_stc <= prev_signal and curr_stc > curr_signal:
                    stc_cross_signal = 1.0  # 金叉
                elif prev_stc >= prev_signal and curr_stc < curr_signal:
                    stc_cross_signal = -1.0  # 死叉
                else:
                    stc_cross_signal = 0.0

                # STC位置信号
                if curr_stc < 20:
                    stc_position_signal = 0.5  # 超卖
                elif curr_stc > 80:
                    stc_position_signal = -0.5  # 超买
                else:
                    stc_position_signal = 0.0

                signal_strength += (stc_cross_signal * 0.3 + stc_position_signal * 0.1)

            # 信号强度限制
            signal_strength = max(-1.0, min(1.0, signal_strength))

        except Exception as e:
            print(f"主信号生成错误: {e}")
            signal_strength = 0.0

        return signal_strength

    def exec_signal(self):
        """执行交易信号 - 无限易Pro标准接口"""
        try:
            self.signal_price = 0

            position = self.get_position(self.params_map.instrument_id)

            # 取消未成交订单
            if self.order_id is not None:
                self.cancel_order(self.order_id)

            # 平仓信号
            if position.net_position > 0 and self.sell_signal:
                self.signal_price = -self.short_price

                if self.trading:
                    self.order_id = self.auto_close_position(
                        exchange=self.params_map.exchange,
                        instrument_id=self.params_map.instrument_id,
                        price=self.short_price,
                        volume=position.net_position,
                        order_direction="sell"
                    )
            elif position.net_position < 0 and self.cover_signal:
                self.signal_price = self.long_price

                if self.trading:
                    self.order_id = self.auto_close_position(
                        exchange=self.params_map.exchange,
                        instrument_id=self.params_map.instrument_id,
                        price=self.long_price,
                        volume=abs(position.net_position),
                        order_direction="buy"
                    )

            # 开仓信号
            if self.short_signal:
                self.signal_price = -self.short_price

                if self.trading:
                    self.order_id = self.send_order(
                        exchange=self.params_map.exchange,
                        instrument_id=self.params_map.instrument_id,
                        volume=self.params_map.order_volume,
                        price=self.short_price,
                        order_direction="sell"
                    )
            elif self.buy_signal:
                self.signal_price = self.long_price

                if self.trading:
                    self.order_id = self.send_order(
                        exchange=self.params_map.exchange,
                        instrument_id=self.params_map.instrument_id,
                        volume=self.params_map.order_volume,
                        price=self.long_price,
                        order_direction="buy"
                    )

        except Exception as e:
            print(f"信号执行错误: {e}")

    def _set_trading_prices(self, kline: KLineData):
        """设置交易价格"""
        self.long_price = self.short_price = kline.close

        if self.tick:
            self.long_price = self.tick.ask_price1
            self.short_price = self.tick.bid_price1

            if self.params_map.price_type == "D2":
                self.long_price = self.tick.ask_price2
                self.short_price = self.tick.bid_price2

    def _calculate_hull_ma(self, price: float) -> Optional[float]:
        """计算Hull移动平均"""
        try:
            if not hasattr(self, 'hull_ma_calculator'):
                self.hull_ma_calculator = HullMovingAverage(self.params_map.hull_period)

            return self.hull_ma_calculator.update(price)
        except Exception as e:
            print(f"Hull MA计算错误: {e}")
            return None

    def _calculate_stc(self, high: float, low: float, close: float) -> Tuple[Optional[float], Optional[float]]:
        """计算STC指标"""
        try:
            if not hasattr(self, 'stc_calculator'):
                self.stc_calculator = SchaffTrendCycle(
                    self.params_map.stc_fast_period,
                    self.params_map.stc_slow_period,
                    self.params_map.stc_cycle_period
                )

            return self.stc_calculator.update(high, low, close)
        except Exception as e:
            print(f"STC计算错误: {e}")
            return None, None

    def _calculate_volatility(self, price: float):
        """计算波动率"""
        try:
            if not hasattr(self, 'price_history'):
                self.price_history = deque(maxlen=50)

            self.price_history.append(price)

            if len(self.price_history) >= 20:
                prices = np.array(list(self.price_history)[-20:])
                returns = np.diff(prices) / prices[:-1]
                volatility = float(np.std(returns))
                self.state_map.volatility = volatility
            else:
                self.state_map.volatility = 0.02

        except Exception as e:
            print(f"波动率计算错误: {e}")
            self.state_map.volatility = 0.02

    def _calculate_trend_strength(self):
        """计算趋势强度"""
        try:
            if len(self.hull_history) >= 5:
                recent_hull = list(self.hull_history)[-5:]
                trend = (recent_hull[-1] - recent_hull[0]) / recent_hull[0]
                self.state_map.trend_strength = max(-1, min(1, trend * 10))
            else:
                self.state_map.trend_strength = 0

        except Exception as e:
            print(f"趋势强度计算错误: {e}")
            self.state_map.trend_strength = 0

    def _apply_fuzzy_correction(self, primary_signal: float, kline: KLineData) -> float:
        """应用模糊推理校正"""
        try:
            if not self.fuzzy_system:
                return primary_signal

            # 准备模糊推理输入
            market_data = {
                'trend': self.state_map.trend_strength,
                'volatility': self.state_map.volatility,
                'volume': 1.0  # 简化处理
            }

            # 执行模糊推理
            fuzzy_results = self.fuzzy_system.process_signal(market_data)

            # 计算模糊校正信号
            correction = 0.0
            if 'strong_buy' in fuzzy_results:
                correction = fuzzy_results['strong_buy'] * 0.8
            elif 'buy' in fuzzy_results:
                correction = fuzzy_results['buy'] * 0.6
            elif 'weak_buy' in fuzzy_results:
                correction = fuzzy_results['weak_buy'] * 0.3
            elif 'strong_sell' in fuzzy_results:
                correction = -fuzzy_results['strong_sell'] * 0.8
            elif 'sell' in fuzzy_results:
                correction = -fuzzy_results['sell'] * 0.6
            elif 'weak_sell' in fuzzy_results:
                correction = -fuzzy_results['weak_sell'] * 0.3

            # 融合主信号和模糊校正
            corrected_signal = primary_signal * 0.7 + correction * 0.3
            return max(-1, min(1, corrected_signal))

        except Exception as e:
            print(f"模糊推理校正错误: {e}")
            return primary_signal

    def _apply_control_correction(self, fuzzy_signal: float, kline: KLineData) -> float:
        """应用控制论校正"""
        try:
            if not self.control_system:
                return fuzzy_signal

            # 控制论处理
            control_results = self.control_system.process_signal(
                fuzzy_signal, kline.close, kline.close * (1 + fuzzy_signal * 0.02)
            )

            self.state_map.system_stability = control_results['stability']
            return control_results['corrected_signal']

        except Exception as e:
            print(f"控制论校正错误: {e}")
            return fuzzy_signal

    def _apply_ml_prediction(self, control_signal: float, kline: KLineData) -> float:
        """应用机器学习预测"""
        try:
            if not self.ml_system:
                return control_signal

            # 机器学习处理
            ml_results = self.ml_system.process_signal(control_signal, kline.close, kline.volume)

            return ml_results['adjusted_signal']

        except Exception as e:
            print(f"机器学习预测错误: {e}")
            return control_signal

# ==================== 工厂函数 ====================

def create_strategy() -> Strategy3:
    """创建Strategy3实例 - 无限易Pro兼容"""
    return Strategy3()

# ==================== 主程序入口 ====================

if __name__ == "__main__":
    # 测试代码
    try:
        strategy = create_strategy()
        print("Strategy3 高级模糊推理策略创建成功")
        print("策略特性:")
        print(f"- Hull MA周期: {strategy.params_map.hull_period.default if hasattr(strategy.params_map.hull_period, 'default') else strategy.params_map.hull_period}")
        print(f"- STC参数: {strategy.params_map.stc_fast_period.default if hasattr(strategy.params_map.stc_fast_period, 'default') else strategy.params_map.stc_fast_period}/{strategy.params_map.stc_slow_period.default if hasattr(strategy.params_map.stc_slow_period, 'default') else strategy.params_map.stc_slow_period}/{strategy.params_map.stc_cycle_period.default if hasattr(strategy.params_map.stc_cycle_period, 'default') else strategy.params_map.stc_cycle_period}")
        print(f"- 模糊推理: {'启用' if (strategy.params_map.enable_fuzzy.default if hasattr(strategy.params_map.enable_fuzzy, 'default') else strategy.params_map.enable_fuzzy) else '禁用'}")
        print(f"- 控制论: {'启用' if (strategy.params_map.enable_control.default if hasattr(strategy.params_map.enable_control, 'default') else strategy.params_map.enable_control) else '禁用'}")
        print(f"- 机器学习: {'启用' if (strategy.params_map.enable_ml.default if hasattr(strategy.params_map.enable_ml, 'default') else strategy.params_map.enable_ml) else '禁用'}")
        print(f"- 信号阈值: {strategy.params_map.signal_threshold.default if hasattr(strategy.params_map.signal_threshold, 'default') else strategy.params_map.signal_threshold}")
        stop_loss = strategy.params_map.stop_loss_pct.default if hasattr(strategy.params_map.stop_loss_pct, 'default') else strategy.params_map.stop_loss_pct
        take_profit = strategy.params_map.take_profit_pct.default if hasattr(strategy.params_map.take_profit_pct, 'default') else strategy.params_map.take_profit_pct
        print(f"- 止损/止盈: {stop_loss:.1%}/{take_profit:.1%}")

        # 显示主图和副图指标
        print("\n主图指标:")
        for key, value in strategy.main_indicator_data.items():
            print(f"  {key}: {value}")

        print("\n副图指标:")
        for key, value in strategy.sub_indicator_data.items():
            print(f"  {key}: {value}")

    except Exception as e:
        print(f"策略创建失败: {e}")

# ==================== 策略说明 ====================
"""
Strategy3 高级模糊推理交易策略

本策略完全兼容无限易Pro交易软件架构，具有以下特点：

1. 架构兼容性：
   - 继承BaseStrategy基类
   - 使用Params和State类管理参数和状态
   - 实现标准的on_tick, callback, on_start等接口
   - 支持主图和副图指标显示

2. 技术指标：
   - Hull移动平均线：快速响应价格变化
   - STC指标：Schaff趋势周期，识别趋势转折点

3. 智能决策系统：
   - 模糊推理：处理市场不确定性
   - 控制论：系统稳定性分析
   - 机器学习：自适应预测

4. 风险管理：
   - 可配置的止损止盈
   - 信号阈值控制
   - 仓位管理

5. 参数配置：
   所有参数通过Params类统一管理，支持界面配置

使用方法：
1. 在无限易Pro中导入此策略文件
2. 配置交易所、合约等基础参数
3. 调整技术指标参数和风险管理参数
4. 启动策略进行交易

注意事项：
- 策略需要足够的历史数据进行指标计算
- 建议先在模拟环境中测试
- 根据不同市场调整参数设置
"""

# ==================== 工厂函数 ====================

def create_strategy3() -> Strategy3:
    """创建Strategy3实例"""
    return Strategy3()